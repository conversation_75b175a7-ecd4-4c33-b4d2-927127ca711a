package context

import (
	"path"
	"strings"

	"github.com/precize/common"
)

type tagStruct struct {
	key   string
	value string
}

func getTagContextOfResource(resourceContext *ResourceContext, resourcesDoc map[string]any,
	resourceContextInsertDoc *common.ResourceContextInsertDoc) (resourceNameTagValue string, tags []tagStruct) {

	var (
		uniqueOwners        = make(map[string]struct{})
		uniqueDeployments   = make(map[string]struct{})
		uniqueSoftwares     = make(map[string]struct{})
		uniqueApps          = make(map[string]struct{})
		uniqueCompliances   = make(map[string]struct{})
		uniqueSensitivities = make(map[string]struct{})
		uniqueEnvironments  = make(map[string]struct{})
		uniqueTeams         = make(map[string]struct{})
		uniqueFileNames     = make(map[string]struct{})
		uniqueRepoNames     = make(map[string]struct{})
		uniqueCommitIDs     = make(map[string]struct{})
	)

	if resourceTags, ok := resourcesDoc["tags"].([]any); ok {

		for _, resourceTag := range resourceTags {

			if resourceTagMap, ok := resourceTag.(map[string]any); ok {

				if tagValue, ok := resourceTagMap["value"].(string); ok {

					if tagKey, ok := resourceTagMap["key"].(string); ok {

						tags = append(tags, tagStruct{key: tagKey, value: tagValue})

						if isOwnerKey(tagKey) {

							var values []string

							if strings.Contains(tagValue, ",") {
								values = strings.Split(tagValue, ",")
							} else {
								values = []string{tagValue}
							}

							for _, value := range values {

								if strings.HasPrefix(value, "arn:") {
									// Special case if value is an aws arn of user or role
									value = path.Base(value)
								}

								if _, ok := uniqueOwners[DEFINED_OWNER+value]; !ok {

									uniqueOwners[DEFINED_OWNER+value] = struct{}{}

									if IsNonHumanEmail(value, resourceContext) {
										uniqueGroupEmailIdentities := make(map[string]struct{})
										getNonHumanEmailOwners(resourceContext, value, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+value+" has been defined as resource owner via tag: "+tagKey, 1)
									}

									resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
										resourceContext.GetUserContextItem(value, TAG_USERTYPE_PREFIX+tagKey, "User has been defined as resource owner via tag: "+tagKey, "", nil),
									)
								}
							}
						}

						if IsDescriptionKey(tagKey) {

							if softwareNames := GetSoftwareNameListFromValue(tagValue); len(softwareNames) > 0 {

								for _, softwareName := range softwareNames {
									if _, ok := uniqueSoftwares[softwareName]; !ok {

										uniqueSoftwares[softwareName] = struct{}{}

										resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
											resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
											common.ResourceContextItem{
												Name: softwareName,
												Type: TAG_SOFTWARETYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							if appNames := GetAppNameListFromValue(tagValue, WithResourceType(resourceContextInsertDoc.ResourceType)); len(appNames) > 0 {

								for _, appName := range appNames {
									if _, ok := uniqueApps[appName]; !ok {

										uniqueApps[appName] = struct{}{}

										resourceContextInsertDoc.ResourceAppTypes.DerivedApp = append(
											resourceContextInsertDoc.ResourceAppTypes.DerivedApp,
											common.ResourceContextItem{
												Name: appName,
												Type: TAG_APPTYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							if teamNames := GetTeamNameListFromValue(tagValue); len(teamNames) > 0 {

								for _, teamName := range teamNames {
									if _, ok := uniqueTeams[teamName]; !ok {

										uniqueTeams[teamName] = struct{}{}

										resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(
											resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
											common.ResourceContextItem{
												Name: teamName,
												Type: TAG_TEAMTYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							if deploymentNames := GetDeploymentNamesFromValue(tagValue); len(deploymentNames) > 0 {

								for _, deploymentName := range deploymentNames {
									if _, ok := uniqueDeployments[deploymentName]; !ok {

										uniqueDeployments[deploymentName] = struct{}{}

										resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
											resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
											common.ResourceContextItem{
												Name: deploymentName,
												Type: TAG_DEPLOYMENTTYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							if sensitivityNames := GetSensitivityNameListFromValue(tagValue); len(sensitivityNames) > 0 {

								for _, sensitivityName := range sensitivityNames {
									if _, ok := uniqueSensitivities[sensitivityName]; !ok {

										uniqueSensitivities[sensitivityName] = struct{}{}

										resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(
											resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
											common.ResourceContextItem{
												Name: sensitivityName,
												Type: TAG_SENSITIVITYTYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							if complianceNames := GetComplianceNameListFromValue(tagValue); len(complianceNames) > 0 {

								for _, complianceName := range complianceNames {
									if _, ok := uniqueCompliances[complianceName]; !ok {

										uniqueCompliances[complianceName] = struct{}{}

										resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance = append(
											resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance,
											common.ResourceContextItem{
												Name: complianceName,
												Type: TAG_COMPLIANCETYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							users := separateEmailsFromDescription(tagValue)

							names := getNamesFromDescription(tagValue, resourceContext.TenantID)

							names = removeNamesOfTaggedEmails(names, users)
							users = append(users, names...)

							for _, user := range users {
								if _, ok := uniqueOwners[DERIVED_OWNER+user]; !ok {

									uniqueOwners[DERIVED_OWNER+user] = struct{}{}

									if IsNonHumanEmail(user, resourceContext) {
										uniqueGroupEmailIdentities := make(map[string]struct{})
										getNonHumanEmailOwners(resourceContext, user, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+user+" has been derived from resource description", 1)
									}

									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
										resourceContext.GetUserContextItem(user, TAG_USERTYPE_PREFIX+tagKey, "User has been derived from resource description", "", nil),
									)
								}
							}
						}

						for envTagKey := range EnvTagKeys {

							if strings.Contains(strings.ToLower(tagKey), envTagKey) {

								if envName := GetEnvironmentNameFromValue(tagValue); len(envName) > 0 {

									if _, ok := uniqueEnvironments[envName]; !ok {

										uniqueEnvironments[envName] = struct{}{}

										resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv = append(
											resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv,
											common.ResourceContextItem{
												Name: envName,
												Type: TAG_ENVTYPE_PREFIX + tagKey,
											},
										)

										accountID, _ := resourcesDoc["accountId"].(string)
										resourceGroup, _ := resourcesDoc["resourceGroup"].(string)
										incrementParentChildEnvCount(resourceContext, envName, accountID, resourceGroup)
									}
								}
							}
						}

						// Software components are sometimes tagged as application. Hence need to check if tag value for app or application is software or not
						var tagValueSoftware bool

						if _, ok := SoftwareTagKeys[strings.ToLower(tagKey)]; ok {

							if softwareName := GetSoftwareNameFromValue(tagValue); len(softwareName) > 0 {

								tagValueSoftware = true

								if _, ok := uniqueSoftwares[softwareName]; !ok {

									uniqueSoftwares[softwareName] = struct{}{}

									resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
										resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
										common.ResourceContextItem{
											Name: softwareName,
											Type: TAG_SOFTWARETYPE_PREFIX + tagKey,
										},
									)
								}
							}
						}

						var skipAppTags bool

						// Cluster resources automatically assigns kubernetes app inside app tag which can be random string
						if strings.Contains(resourceContextInsertDoc.ResourceID, "/k8s/") && resourceContextInsertDoc.ServiceID == common.GCP_SERVICE_ID_INT {
							switch resourceContextInsertDoc.ResourceType {
							case common.GCP_GKENODE_RESOURCE_TYPE:
							default:
								skipAppTags = true
							}
						}

						if _, ok := AppTagKeys[strings.ToLower(tagKey)]; ok && len(tagValue) > 0 && !skipAppTags && !tagValueSoftware {

							var appNameTagValue string

							if appName := GetAppNameFromValue(tagValue, WithResourceType(resourceContextInsertDoc.ResourceType)); len(appName) > 0 {
								appNameTagValue = appName
							} else {
								appNameTagValue = FormatContextValue(tagValue)
								// Add new global app - future scans will pick it up
								AddApplicationToGlobalApps(appNameTagValue)
							}

							if _, ok := uniqueApps[appNameTagValue]; !ok {

								uniqueApps[appNameTagValue] = struct{}{}

								resourceContextInsertDoc.ResourceAppTypes.DefinedApp = append(
									resourceContextInsertDoc.ResourceAppTypes.DefinedApp,
									common.ResourceContextItem{
										Name: appNameTagValue,
										Type: TAG_APPTYPE_PREFIX + tagKey,
									},
								)
							}
						}

						// move this from here
						if resourceContextInsertDoc.ResourceType == common.GCP_GKENODE_RESOURCE_TYPE ||
							resourceContextInsertDoc.ResourceType == common.GCP_GKECLUSTER_RESOURCE_TYPE ||
							strings.Contains(resourceContextInsertDoc.ResourceType, "k8s.io") {

							if _, ok := uniqueDeployments[KUBERNETES_DEPLOYMENT]; !ok {

								uniqueDeployments[KUBERNETES_DEPLOYMENT] = struct{}{}

								resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = append(
									resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment,
									common.ResourceContextItem{
										Name: KUBERNETES_DEPLOYMENT,
										Type: common.RESOURCETYPE_DEPLOYMENT_TYPE,
									},
								)
							}
						}

						if isTeamKey(tagKey) && len(tagValue) > 0 {

							team := FormatContextValue(tagValue)

							if teamName := GetTeamNameFromValue(tagValue); strings.ToLower(teamName) == strings.ToLower(team) {
								// If it is just a case change, get proper case so that it does not create a new team
								team = teamName
							} else {
								// Add new global team - future scans will pick it up
								AddTeamToGlobalTeams(team)
							}

							if _, ok := uniqueTeams[team]; !ok {

								uniqueTeams[team] = struct{}{}

								resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam = append(
									resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam,
									common.ResourceContextItem{
										Name: team,
										Type: TAG_TEAMTYPE_PREFIX + tagKey,
									},
								)
							}
						} else {

							if teamName := GetTeamNameFromValue(tagValue); len(teamName) > 0 {

								team := teamName

								if _, ok := uniqueTeams[team]; !ok {

									uniqueTeams[team] = struct{}{}

									resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam = append(
										resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam,
										common.ResourceContextItem{
											Name: team,
											Type: TAG_TEAMTYPE_PREFIX + tagKey,
										},
									)
								}
							}
						}

						if deploymentNames := GetDeploymentNamesFromValue(tagKey); len(deploymentNames) > 0 {

							for _, deploymentName := range deploymentNames {

								if !strings.Contains(tagValue, "false") {

									if _, ok := uniqueDeployments[deploymentName]; !ok {

										uniqueDeployments[deploymentName] = struct{}{}

										resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = append(
											resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment,
											common.ResourceContextItem{
												Name: deploymentName,
												Type: TAG_DEPLOYMENTTYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}
						}

						if deploymentNames := GetDeploymentNamesFromValue(tagValue); len(deploymentNames) > 0 {

							for _, deploymentName := range deploymentNames {

								if _, ok := uniqueDeployments[deploymentName]; !ok {

									uniqueDeployments[deploymentName] = struct{}{}

									resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = append(
										resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment,
										common.ResourceContextItem{
											Name: deploymentName,
											Type: TAG_DEPLOYMENTTYPE_PREFIX + tagKey,
										},
									)
								}
							}
						}

						if isDataResourceType(resourceContextInsertDoc.ResourceType) {

							if complianceName := GetComplianceNameFromValue(tagKey); len(complianceName) > 0 {

								if !strings.Contains(tagValue, "false") {

									if _, ok := uniqueCompliances[complianceName]; !ok {

										uniqueCompliances[complianceName] = struct{}{}

										resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance = append(
											resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance,
											common.ResourceContextItem{
												Name: complianceName,
												Type: TAG_COMPLIANCETYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							if complianceName := GetComplianceNameFromValue(tagValue); len(complianceName) > 0 {

								if _, ok := uniqueCompliances[complianceName]; !ok {

									uniqueCompliances[complianceName] = struct{}{}

									resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance = append(
										resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance,
										common.ResourceContextItem{
											Name: complianceName,
											Type: TAG_COMPLIANCETYPE_PREFIX + tagKey,
										},
									)
								}
							}

							if sensitivityName := GetSensitivityNameFromValue(tagKey); len(sensitivityName) > 0 {

								if !strings.Contains(tagValue, "false") {

									if _, ok := uniqueSensitivities[sensitivityName]; !ok {

										uniqueSensitivities[sensitivityName] = struct{}{}

										resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity = append(
											resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity,
											common.ResourceContextItem{
												Name: sensitivityName,
												Type: TAG_SENSITIVITYTYPE_PREFIX + tagKey,
											},
										)
									}
								}
							}

							if sensitivityName := GetSensitivityNameFromValue(tagValue); len(sensitivityName) > 0 {

								if _, ok := uniqueSensitivities[sensitivityName]; !ok {

									uniqueSensitivities[sensitivityName] = struct{}{}

									resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity = append(
										resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity,
										common.ResourceContextItem{
											Name: sensitivityName,
											Type: TAG_SENSITIVITYTYPE_PREFIX + tagKey,
										},
									)
								}
							}
						}

						if isCostCenterKey(tagKey) {

							resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter = append(
								resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter,
								common.ResourceContextItem{
									Name: common.ConvertToTitleCase(tagValue),
									Type: TAG_COSTCENTER_PREFIX + tagKey,
								},
							)
						}

						if isRepoNameKey(tagKey) {
							if _, ok := uniqueRepoNames[tagValue]; !ok {
								uniqueRepoNames[tagValue] = struct{}{}

								resourceContextInsertDoc.CommitInfo.RepoName = tagValue
							}
						}

						if isFileNameKey(tagKey) {
							if _, ok := uniqueFileNames[tagValue]; !ok {
								uniqueFileNames[tagValue] = struct{}{}

								resourceContextInsertDoc.CommitInfo.FileName = tagValue
							}
						}

						if isCommitIDKey(tagKey) {
							if _, ok := uniqueCommitIDs[tagValue]; !ok {
								uniqueCommitIDs[tagValue] = struct{}{}
							}
						}

						if tagKey == "aws:cloudformation:stack-id" {
							// Assign cft stack as parent related resource for resources created by stack
							stackID := strings.ToLower(tagValue)
							stackDocID := common.GenerateCombinedHashID(stackID, common.AWS_CFTSTACK_RESOURCE_TYPE, resourceContextInsertDoc.Account, resourceContext.LastCollectedAt, resourceContextInsertDoc.TenantID)
							resourceDocID := common.GenerateCombinedHashID(resourceContextInsertDoc.ResourceID, resourceContextInsertDoc.ResourceType, resourceContextInsertDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)

							assignRelatedParentResource(resourceContext, resourceContextInsertDoc.ResourceID, resourceDocID, resourceContextInsertDoc.ResourceType,
								stackID, stackDocID, common.AWS_CFTSTACK_RESOURCE_TYPE, true)
						}

						if tagKey == "Name" {
							resourceNameTagValue = tagValue
						}
					}
				}
			}
		}
	}

	if len(uniqueCommitIDs) > 0 {
		getCodeOwnerFromCommitIDs(uniqueCommitIDs, resourceContext, resourceContextInsertDoc)
	}

	return
}
